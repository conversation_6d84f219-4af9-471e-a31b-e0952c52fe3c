from typing import Any, List, Optional, Dict
from pricesmart_common.utils import async_execute_query, get_str_repr, get_array_format
from chat import queries as chat_queries
from chat import models as chat_models
from chat import constants as chat_constants
from configuration.environment import environment
from logger.logger import logger
from exceptions.exceptions import NotFoundException, InvalidAccessException


async def get_topics_by_objects(
    object_type: str, object_ids: Optional[List[str]] = None, user_ids: Optional[List[int]] = None
) -> List[dict]:
    """Get all topics for objects of a specific type for one or more users"""
    if object_ids:
        # Query for specific object IDs
        if not user_ids:
            user_ids = [0]  # Default to user_id 0 if none provided

        query = chat_queries.GET_TOPICS_BY_OBJECTS.format(
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            object_type=get_str_repr(object_type),
            object_ids=get_array_format(object_ids),
            user_ids=get_array_format(user_ids),
        )
    else:
        # Query for all objects of the specified type
        if not user_ids:
            user_ids = [0]  # Default to user_id 0 if none provided

        query = chat_queries.GET_TOPICS_BY_OBJECT_TYPE.format(
            promo_schema=environment.promo_schema,
            global_schema=environment.global_schema,
            object_type=get_str_repr(object_type),
            user_id=user_ids[0] if user_ids else 0,  # Keep backward compatibility for single user
        )

    logger.info(f"Executing query: {query}")
    data = await async_execute_query(query)
    return data


async def get_topics_by_objects_grouped_by_user(
    object_type: str, object_ids: List[str], user_ids: List[int]
) -> Dict[int, List[dict]]:
    """
    Get topic data for multiple users and group results by user_id.

    Args:
        object_type: The object type (promo, event, store_group, product)
        object_ids: List of object IDs
        user_ids: List of user IDs to get data for

    Returns:
        Dict mapping user_id to their topic data
    """
    if not object_ids or not user_ids:
        return {}

    # Use the combined query
    data = await get_topics_by_objects(object_type, object_ids, user_ids)

    # Group data by user_id
    user_data_map = {}
    for row in data:
        user_id = row["user_id"]
        if user_id not in user_data_map:
            user_data_map[user_id] = []

        user_data_map[user_id].append(
            {
                "obj_id": row["obj_id"],
                "topics_count": row["topics_count"],
                "message_count": row["message_count"],
                "unread_count": row["unread_count"],
                "obj_type": row["obj_type"],
            }
        )

    return user_data_map


async def verify_topic_exists(topic_id: str) -> bool:
    """Simple verification that a topic exists (for internal use)"""
    query = chat_queries.GET_TOPIC_BY_ID.format(
        promo_schema=environment.promo_schema, global_schema=environment.global_schema, topic_id=topic_id
    )
    data = await async_execute_query(query)
    return len(data) > 0 if data else False


async def create_topic(topic_data: chat_models.ChatTopicCreate, user_id: int) -> str:
    """Create a new chat topic with optimized batch operations"""
    # Only include the creator as a member
    all_user_ids = [user_id]

    # Use optimized single query to create topic, add members, and create unread entries
    query = chat_queries.CREATE_TOPIC_WITH_MEMBERS_AND_UNREAD.format(
        promo_schema=environment.promo_schema,
        name=get_str_repr(topic_data.name),
        description=get_str_repr(topic_data.description) if topic_data.description else "NULL",
        object_type=get_str_repr(topic_data.object_type),
        object_ids=get_array_format(topic_data.object_ids),
        created_by=user_id,
        all_user_ids=get_array_format(all_user_ids),
    )

    logger.info(f"Executing optimized create_topic query for {len(all_user_ids)} members {query}")

    result = await async_execute_query(query)
    topic_id = result[0]["topic_id"]

    return topic_id


async def update_topic(topic_id: str, topic_data: chat_models.ChatTopicUpdate, user_id: int) -> bool:
    """Update a chat topic"""
    query = chat_queries.UPDATE_TOPIC.format(
        promo_schema=environment.promo_schema,
        name=get_str_repr(topic_data.name) if topic_data.name else "NULL",
        description=get_str_repr(topic_data.description) if topic_data.description else "NULL",
        status=get_str_repr(topic_data.status.value) if topic_data.status else "NULL",
        updated_by=user_id,
        topic_id=topic_id,
    )
    await async_execute_query(query)
    return True


async def delete_topic(topic_id: str, user_id: int) -> bool:
    """Archive a chat topic (soft delete)"""
    # Get topic info before deletion
    topic_exists = await verify_topic_exists(topic_id)
    if not topic_exists:
        raise NotFoundException(chat_constants.TOPIC_NOT_FOUND)

    # Reset all unread counts to 0
    await reset_all_unread_counts_for_topic(topic_id)

    # Archive the topic
    query = chat_queries.DELETE_TOPIC.format(
        promo_schema=environment.promo_schema, updated_by=user_id, topic_id=topic_id
    )
    await async_execute_query(query)

    return True


async def add_topic_members(topic_id: str, user_ids: List[int], created_by: int) -> bool:
    """Add members to a chat topic"""
    # Create unread entries for new members in batch
    await create_multiple_unread_entries(topic_id, user_ids)

    query = chat_queries.ADD_TOPIC_MEMBERS.format(
        promo_schema=environment.promo_schema,
        topic_id=topic_id,
        user_ids=get_array_format(user_ids),
        created_by=created_by,
    )
    await async_execute_query(query)

    return True


async def remove_topic_members(topic_id: str, user_ids: List[int]) -> List[int]:
    """Remove multiple members from a chat topic using a single query"""
    query = chat_queries.REMOVE_TOPIC_MEMBERS.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, user_ids=get_array_format(user_ids)
    )

    result = await async_execute_query(query)
    removed_user_ids = [row["user_id"] for row in result] if result else []

    return removed_user_ids


async def get_topic_members(topic_id: str) -> List[dict]:
    """Get all members of a chat topic"""
    query = chat_queries.GET_TOPIC_MEMBERS.format(
        promo_schema=environment.promo_schema, global_schema=environment.global_schema, topic_id=topic_id
    )
    return await async_execute_query(query)


async def check_user_is_member(topic_id: str, user_id: int) -> bool:
    """Check if user is a member of the topic"""
    query = chat_queries.CHECK_USER_IS_MEMBER.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, user_id=user_id
    )
    result = await async_execute_query(query)
    return result[0]["count"] > 0 if result else False


async def get_messages_by_topic_paginated(topic_id: str, limit: int = 50, offset: int = 0, search: Optional[str] = None) -> List[dict]:
    """Get paginated messages for a specific topic with optional search"""
    # Build search condition
    search_condition = ""
    if search and search.strip():
        search_condition = f"AND cm.content ILIKE '%{search.strip()}%'"
    
    query = chat_queries.GET_MESSAGES_BY_TOPIC_PAGINATED.format(
        promo_schema=environment.promo_schema, 
        global_schema=environment.global_schema, 
        topic_id=topic_id,
        search_condition=search_condition,
        limit=limit,
        offset=offset
    )
    return await async_execute_query(query)


async def get_messages_count_by_topic(topic_id: str, search: Optional[str] = None) -> int:
    """Get total count of messages for a specific topic with optional search"""
    # Build search condition
    search_condition = ""
    if search and search.strip():
        search_condition = f"AND cm.content ILIKE '%{search.strip()}%'"
    
    query = chat_queries.GET_MESSAGES_COUNT_BY_TOPIC.format(
        promo_schema=environment.promo_schema, 
        topic_id=topic_id,
        search_condition=search_condition
    )
    result = await async_execute_query(query)
    return result[0]["total_count"] if result else 0


async def clear_messages_by_topic(topic_id: str) -> bool:
    """Clear all messages in a topic by marking them as deleted"""
    query = chat_queries.CLEAR_MESSAGES_BY_TOPIC.format(
        promo_schema=environment.promo_schema, topic_id=topic_id
    )
    await async_execute_query(query)
    return True


async def create_message(message_data: chat_models.ChatMessageCreate, user_id: int) -> str:
    """Create a new message"""
    query = chat_queries.CREATE_MESSAGE.format(
        promo_schema=environment.promo_schema,
        topic_id=message_data.topic_id,
        sender_id=user_id,
        content=get_str_repr(message_data.content),
        tagged_users=get_array_format(message_data.tagged_users) if message_data.tagged_users else "NULL",
        priority=get_str_repr(message_data.priority.value),
        reply_to=get_str_repr(message_data.reply_to) if message_data.reply_to else "NULL",
    )

    result = await async_execute_query(query)
    message_id = result[0]["id"]

    return message_id


async def update_message(message_id: str, message_data: chat_models.ChatMessageUpdate, user_id: int) -> bool:
    """Update a message"""
    query = chat_queries.UPDATE_MESSAGE.format(
        promo_schema=environment.promo_schema,
        content=get_str_repr(message_data.content),
        is_pinned=message_data.is_pinned if message_data.is_pinned is not None else "NULL",
        message_id=message_id,
    )
    await async_execute_query(query)

    return True


async def delete_message(message_id: str, user_id: int) -> bool:
    """Delete a message"""
    query = chat_queries.DELETE_MESSAGE.format(promo_schema=environment.promo_schema, message_id=message_id)
    await async_execute_query(query)

    return True


async def get_message_by_id(message_id: str) -> Optional[dict]:
    """Get a specific message by ID"""
    query = chat_queries.GET_MESSAGE_BY_ID.format(
        promo_schema=environment.promo_schema,
        global_schema=environment.global_schema,
        message_id=message_id,
    )
    data = await async_execute_query(query)
    return data[0] if data else None


async def get_unread_counts_by_user(user_id: int) -> List[dict]:
    """Get unread counts for all topics for a user"""
    query = chat_queries.GET_UNREAD_COUNTS_BY_USER.format(promo_schema=environment.promo_schema, user_id=user_id)
    return await async_execute_query(query)


async def get_unread_count_by_topic(topic_id: str) -> List[dict]:
    """Get unread counts for all users in a topic"""
    query = chat_queries.GET_UNREAD_COUNT_BY_TOPIC.format(
        promo_schema=environment.promo_schema, global_schema=environment.global_schema, topic_id=topic_id
    )
    return await async_execute_query(query)


async def get_total_unread_by_object(object_type: str, object_id: str, user_id: int) -> int:
    """Get total unread count for an object for a user"""
    query = chat_queries.GET_TOTAL_UNREAD_BY_OBJECT.format(
        promo_schema=environment.promo_schema,
        object_type=get_str_repr(object_type),
        object_id=get_str_repr(object_id),
        user_id=user_id,
    )
    result = await async_execute_query(query)
    return result[0]["total_unread"] if result else 0


async def create_unread_entry(topic_id: str, user_id: int) -> bool:
    """Create unread entry for a user in a topic"""
    query = chat_queries.CREATE_UNREAD_ENTRY.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, user_id=user_id
    )
    await async_execute_query(query)
    return True


async def create_multiple_unread_entries(topic_id: str, user_ids: List[int]) -> bool:
    """Create unread entries for multiple users in a topic"""
    query = chat_queries.CREATE_MULTIPLE_UNREAD_ENTRIES.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, user_ids=get_array_format(user_ids)
    )
    await async_execute_query(query)
    return True


async def update_unread_counts_for_message(topic_id: str, sender_id: int) -> bool:
    """Update unread counts for all members except sender"""
    query = chat_queries.UPDATE_UNREAD_COUNTS_FOR_MESSAGE.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, sender_id=sender_id
    )
    await async_execute_query(query)
    return True


async def update_unread_counts_for_offline_users(topic_id: str, user_ids: List[int]) -> bool:
    """Update unread counts for specific offline users"""
    if not user_ids:
        return True

    query = chat_queries.UPDATE_UNREAD_COUNTS_FOR_OFFLINE_USERS.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, user_ids=get_array_format(user_ids)
    )
    await async_execute_query(query)
    return True


async def reset_unread_count(topic_id: str, user_id: int) -> bool:
    """Reset unread count for a user in a topic"""
    query = chat_queries.RESET_UNREAD_COUNT.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, user_id=user_id
    )
    await async_execute_query(query)
    return True


async def reset_all_unread_counts_for_topic(topic_id: str) -> bool:
    """Reset all unread counts to 0 for a topic"""
    query = chat_queries.RESET_ALL_UNREAD_COUNTS_FOR_TOPIC.format(
        promo_schema=environment.promo_schema, topic_id=topic_id
    )
    await async_execute_query(query)
    return True


async def get_topics_by_object_with_members(object_type: str, object_id: str, user_id: int) -> List[dict]:
    """Get topics for a specific object with members for a user"""
    query = chat_queries.GET_TOPICS_BY_OBJECT_WITH_MEMBERS.format(
        promo_schema=environment.promo_schema,
        global_schema=environment.global_schema,
        object_type=get_str_repr(object_type),
        object_id=get_str_repr(object_id),
        user_id=user_id,
    )

    logger.info(f"Executing get_topics_by_object_with_members query: {query}")
    data = await async_execute_query(query)
    return data


async def get_topics_by_topic_id_with_members(topic_id: str, user_id: int = None) -> List[dict]:
    """Get topics by topic ID with members using the same query structure as object endpoints"""

    query = chat_queries.GET_TOPICS_BY_TOPIC_ID_WITH_MEMBERS.format(
        promo_schema=environment.promo_schema,
        global_schema=environment.global_schema,
        topic_id=topic_id,
        user_id=user_id,
    )

    logger.info(f"Executing get_topics_by_topic_id_with_members query: {query}")
    data = await async_execute_query(query)
    return data


async def pin_topic(topic_id: str, is_pinned: bool, user_id: int) -> dict:
    """Pin or unpin a topic for a user"""

    # Pin/unpin the topic
    query = chat_queries.PIN_TOPIC.format(
        promo_schema=environment.promo_schema, topic_id=topic_id, user_id=user_id, is_pinned=is_pinned
    )

    logger.info(f"Executing pin_topic query: {query}")
    await async_execute_query(query)

    # Return the updated topic data
    topics = await get_topics_by_topic_id_with_members(topic_id, user_id)
    return topics[0] if topics else {}
